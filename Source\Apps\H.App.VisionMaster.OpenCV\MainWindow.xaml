<h:MainWindow x:Class="H.App.VisionMaster.OpenCV.MainWindow"
              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
              xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
              xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
              xmlns:h="https://github.com/HeBianGu"
              xmlns:lc="clr-namespace:H.App.VisionMaster.OpenCV.Commands"
              xmlns:ld="clr-namespace:H.App.VisionMaster.OpenCV.DiagramDatas"
              xmlns:local="clr-namespace:H.App.VisionMaster.OpenCV"
              xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
              Title="VisionMaster-OpenCV "
              Width="1200"
              Height="750"
              h:Cattach.TitleFontSize="30"
              CaptionHeight="85"
              Cattach.TitleMargin="10 20"
              WindowStartupLocation="CenterScreen"
              mc:Ignorable="d">
    <Window.DataContext>
        <local:MainViewModel />
    </Window.DataContext>
    <h:MainWindow.CaptionTempate>
        <ControlTemplate>
            <DockPanel DataContext="{Binding Project}">
                <Separator Height="20"
                           DockPanel.Dock="Right"
                           Style="{DynamicResource {x:Static SeparatorKeys.Vertical}}" />
                <DockPanel DockPanel.Dock="Right"
                           WindowChrome.IsHitTestVisibleInChrome="True">
                    <FontIconButton Cattach.GuideAssemblyVersion="*******"
                                    Command="{ShowColorThemeViewCommand}"
                                    Style="{DynamicResource {x:Static FontIconButtonKeys.Command}}" />
                    <FontIconButton Command="{ShowSettingCommand}"
                                    Style="{DynamicResource {x:Static FontIconButtonKeys.Command}}" />
                    <ContentPresenter Content="{Ioc Type={x:Type h:ISwitchThemeViewPresenter}}" />
                    <FontIconButton Command="{ShowAboutCommand}"
                                    Style="{DynamicResource {x:Static FontIconButtonKeys.Command}}" />
                    <FontIconButton Command="{ShowGuideCommand}"
                                    Style="{DynamicResource {x:Static FontIconButtonKeys.Command}}" />
                </DockPanel>
                <UniformGrid Margin="0,2"
                             Rows="2">
                    <DockPanel>
                        <Menu Margin="0,1"
                              HorizontalAlignment="Left"
                              Background="Transparent"
                              WindowChrome.IsHitTestVisibleInChrome="True">
                            <Menu.Resources>
                                <Style BasedOn="{StaticResource {x:Static MenuItemKeys.BindCommand}}"
                                       TargetType="MenuItem" />
                            </Menu.Resources>
                            <MenuItem Background="Transparent"
                                      Header="文件">
                                <MenuItem Command="{ShowNewProjectCommand}" />
                                <MenuItem Command="{ShowProjectsCommand}" />
                                <MenuItem Command="{ShowEditProjectCommand}" />
                                <MenuItem Command="{ShowSaveProjectCommand}" />
                                <MenuItem Command="{ShowCurrentProjectFileCommand}" />
                                <MenuItem Cattach.Icon="{x:Static FontIcons.Calendar}"
                                          Header="最近的项目"
                                          ItemsSource="{Binding Source={x:Static IocProject.Instance}, Path=Collection}">
                                    <MenuItem.ItemContainerStyle>
                                        <Style TargetType="MenuItem">
                                            <Setter Property="Command" Value="{ShowOpenProjectCommand}" />
                                            <Setter Property="CommandParameter" Value="{Binding}" />
                                            <Setter Property="Height" Value="50" />
                                            <Setter Property="MinWidth" Value="400" />
                                        </Style>
                                    </MenuItem.ItemContainerStyle>
                                </MenuItem>
                                <Separator />
                                <MenuItem Cattach.Icon="{x:Static FontIcons.PowerButton}"
                                          Command="{CloseAfterSaveWindowCommand}"
                                          CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                                          Header="退出" />
                            </MenuItem>
                            <MenuItem Header="编辑"
                                      ItemsSource="{Binding Commands, IsAsync=True}" />
                            <MenuItem Header="运行"
                                      ItemsSource="{Binding SelectedDiagramData.Commands, IsAsync=True}" />
                            <MenuItem Header="系统">
                                <MenuItem Command="{ShowSettingCommand}" />
                                <MenuItem Command="{ShowLog4netPathCommand}" />
                                <MenuItem Command="{ShowColorThemeViewCommand}" />
                                <MenuItem Command="{ShowSettingCommand SwitchToType={x:Type IThemeOptions}}"
                                          Header="主题设置" />
                                <MenuItem Command="{ShowTreeViewCommand Name=流程功能列表}"
                                          CommandParameter="{Binding SelectedDiagramData.NodeGroups}"
                                          Header="流程功能列表">
                                    <TreeViewPresenter.ItemTemplate>
                                        <HierarchicalDataTemplate ItemsSource="{Binding NodeDatas}">
                                            <DockPanel>
                                                <TextBlock Margin="10,0,0,5"
                                                           DockPanel.Dock="Right"
                                                           FontSize="{DynamicResource {x:Static FontSizeKeys.Header7}}"
                                                           Opacity="0.6"
                                                           Text="{Binding Description}" />
                                                <TextBlock Text="{Binding Name}" />
                                            </DockPanel>
                                        </HierarchicalDataTemplate>
                                    </TreeViewPresenter.ItemTemplate>
                                </MenuItem>
                            </MenuItem>
                            <MenuItem Header="帮助">
                                <MenuItem Command="{ShowGuideCommand}" />
                                <MenuItem Command="{ShowNewGuideCommand}" />
                                <MenuItem Command="{ShowGuideTreeCommand}" />
                                <MenuItem Command="{ShowNewGuideTreeCommand}" />
                                <Separator />
                                <MenuItem Command="{ShowNotImplementedCommand}"
                                          Header="检查更新" />
                                <MenuItem Command="{ShowNotImplementedCommand}"
                                          Header="注册" />

                                <MenuItem Command="{ShowReleaseVersionsCommand}" />
                                <MenuItem Command="{ShowSupportCommand}" />
                                <MenuItem Command="{ShowFeedbackCommand}" />
                                <MenuItem Command="{ShowWebSiteCommand}" />
                                <Separator />
                                <MenuItem Command="{ShowContactCommand}">
                                    <MenuItem Command="{ShowGithubContactCommand}" />
                                    <MenuItem Command="{ShowGitHubIssueContactCommand}" />
                                    <MenuItem Command="{ShowQQContactCommand}" />
                                    <MenuItem Command="{ShowSendMailContactCommand}" />
                                    <MenuItem Command="{ShowBlogContactCommand}" />
                                    <MenuItem Command="{ShowPodcastContactCommand}" />
                                </MenuItem>
                                <MenuItem Command="{ShowPrivacyCommand}">
                                    <MenuItem Command="{ShowAgreementCommand}" />
                                    <MenuItem Command="{ShowPrivacyCommand}" />
                                </MenuItem>
                                <MenuItem Command="{ShowSponsorCommand}" />
                                <MenuItem Command="{ShowAboutCommand}" />
                            </MenuItem>
                        </Menu>

                        <TextBlock Margin="10,0"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Cattach.GuideTitle="显示当前打开的项目名称"
                                   Cattach.UseGuide="True">
                            <Run Text="项目名称：" />
                            <Run Text="{Binding Source={x:Static IocProject.Instance}, Path=Current.Title}" />
                        </TextBlock>
                    </DockPanel>
                    <Border BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                            BorderThickness="0,1,0,0"
                            WindowChrome.IsHitTestVisibleInChrome="True">
                        <DockPanel Margin="0,1"
                                   LastChildFill="False">
                            <DockPanel.Resources>
                                <Style BasedOn="{StaticResource {x:Static FontIconButtonKeys.Command}}"
                                       TargetType="FontIconButton" />
                            </DockPanel.Resources>
                            <FontIconButton Command="{ShowNewProjectCommand}" />
                            <FontIconButton Command="{ShowProjectsOrNewCommand}" />
                            <FontIconButton Command="{ShowEditProjectCommand}" />
                            <FontIconButton Command="{ShowSaveProjectCommand}" />
                            <Separator Style="{DynamicResource {x:Static SeparatorKeys.Vertical}}" />
                            <!--  选中流程图的操作  -->
                            <ItemsControl ItemsSource="{Binding Commands, IsAsync=True}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <FontIconButton Command="{Binding}"
                                                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ItemsControl}, Path=DataContext.SelectedDiagramData.SelectedPart}"
                                                        Content="{Binding Icon}"
                                                        ToolTip="{Binding Name}" />
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal" />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                            </ItemsControl>
                            <Separator Style="{DynamicResource {x:Static SeparatorKeys.Vertical}}" />

                            <!--  选中流程图的操作  -->
                            <ItemsControl ItemsSource="{Binding SelectedDiagramData.Commands, IsAsync=True}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <FontIconButton Command="{Binding}"
                                                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ItemsControl}, Path=DataContext.SelectedDiagramData.SelectedPart}"
                                                        Content="{Binding Icon}"
                                                        ToolTip="{Binding Name}" />
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal" />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                            </ItemsControl>
                            <Separator Style="{DynamicResource {x:Static SeparatorKeys.Vertical}}" />
                            <FontIconButton Command="{h:ShowViewCommand}"
                                            CommandParameter="{Binding SelectedDiagramData.SelectedPartData}" />
                            <FontIconButton Command="{h:ShowTabEditCommand}"
                                            CommandParameter="{Binding SelectedDiagramData.SelectedPartData}" />
                        </DockPanel>
                    </Border>
                </UniformGrid>
            </DockPanel>
        </ControlTemplate>
    </h:MainWindow.CaptionTempate>
    <Grid>
        <Border BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrushTitle}}"
                BorderThickness="0,1,0,0"
                DataContext="{Binding Project}">
            <h:GridSplitterBox GridSpliterBackground="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                               IsExpanded="False"
                               MenuMaxWidth="300"
                               Mode="Extend"
                               ToggleVerticalAlignment="Bottom">
                <h:GridSplitterBox.MenuContent>
                    <Control>
                        <Control.Template>
                            <ControlTemplate>
                                <Grid Margin="0,1">
                                    <GroupBox x:Name="dp_2"
                                              Header="流程资源"
                                              Tag="True">
                                        <Cattach.CaptionRightTemplate>
                                            <ControlTemplate>
                                                <FontIconToggleButton CheckedGlyph="{x:Static FontIcons.AlignLeft}"
                                                                      IsChecked="{Binding RelativeSource={RelativeSource AncestorType=GroupBox}, Path=Tag}"
                                                                      Style="{DynamicResource {x:Static FontIconToggleButtonKeys.Switch}}"
                                                                      UncheckedGlyph="{x:Static FontIcons.CaretBottomRightSolidCenter8}" />
                                            </ControlTemplate>
                                        </Cattach.CaptionRightTemplate>
                                        <Border Margin="0,1,0,40"
                                                BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                                BorderThickness="0,0,0,1">
                                            <Grid>
                                                <ContentPresenter Content="{Binding SelectedDiagramData.NodeGroups, Converter={GetNodeDataGroupsTreeViewPresenter}}"
                                                                  Visibility="{Binding RelativeSource={RelativeSource AncestorType=GroupBox}, Path=Tag, Converter={x:Static h:Converter.GetTrueToCollapsed}}" />
                                                <ContentPresenter Content="{Binding SelectedDiagramData.NodeGroups, Converter={GetNodeDataGroupsItemsControlPresenter}}"
                                                                  Visibility="{Binding RelativeSource={RelativeSource AncestorType=GroupBox}, Path=Tag, Converter={x:Static h:Converter.GetTrueToVisible}}" />

                                            </Grid>
                                        </Border>
                                    </GroupBox>
                                    <ContentPresenter x:Name="dp_1"
                                                      Margin="0,20,0,0"
                                                      Content="{Binding SelectedDiagramData.NodeGroups, Converter={GetNodeDataGroupsContextMenuPresenter}}" />
                                </Grid>
                                <ControlTemplate.Triggers>
                                    <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=h:GridSplitterBox}, Path=MenuWidth.Value, Converter={x:Static h:Converter.GetLessThan}, ConverterParameter=90}"
                                                 Value="True">
                                        <Setter TargetName="dp_1" Property="Visibility" Value="Visible" />
                                        <Setter TargetName="dp_2" Property="Visibility" Value="Collapsed" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=h:GridSplitterBox}, Path=MenuWidth.Value, Converter={x:Static h:Converter.GetGreaterThan}, ConverterParameter=90}"
                                                 Value="True">
                                        <Setter TargetName="dp_2" Property="Visibility" Value="Visible" />
                                        <Setter TargetName="dp_1" Property="Visibility" Value="Collapsed" />
                                    </DataTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Control.Template>
                    </Control>
                </h:GridSplitterBox.MenuContent>
                <DockPanel>
                    <h:GridSplitterBox GridSpliterBackground="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                       MenuMaxWidth="1000"
                                       MenuMinWidth="300"
                                       MenuWidth="600"
                                       Style="{DynamicResource {x:Static h:GridSplitterBox.RightKey}}"
                                       ToggleVerticalAlignment="Bottom"
                                       UseToggle="False">
                        <h:GridSplitterBox.MenuContent>
                            <DockPanel>
                                <StatusBar BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                           BorderThickness="0,1,0,0"
                                           Cattach.GuideTitle="操作消息"
                                           Cattach.UseGuide="True"
                                           DockPanel.Dock="Bottom">
                                    <FontIconTextBlock Margin="10,0"
                                                       Foreground="{DynamicResource {x:Static BrushKeys.Green}}"
                                                       Text="&#xEC58;" />
                                    <TextBlock Text="{Binding SelectedDiagramData.Message, Converter={x:Static Converter.GetDateTimeNowToString}}" />
                                    <TextBlock>
                                        <Run Text="{Binding SelectedDiagramData.Name}" />
                                        <Run Text="{Binding SelectedDiagramData.Message}" />
                                    </TextBlock>
                                </StatusBar>
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="2*"
                                                       MinHeight="300" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition x:Name="r_d"
                                                       MinHeight="{DynamicResource {x:Static h:LayoutKeys.ItemHeight}}" />
                                    </Grid.RowDefinitions>
                                    <!--  可变部分  -->
                                    <ContentPresenter Content="{Binding SelectedDiagramData}">
                                        <ContentPresenter.Resources>
                                            <DataTemplate DataType="{x:Type ld:OpenCVVisionDiagramData}">
                                                <DockPanel x:Name="dp_diagrameData">
                                                    <ContentPresenter Content="{Binding SrcImageNodeData}"
                                                                      DockPanel.Dock="Bottom">
                                                        <ContentPresenter.Resources>
                                                            <DataTemplate DataType="{x:Type OpenCVSrcFilesNodeDataBase}">
                                                                <Expander Cattach.GuideTitle="管理图像源"
                                                                          DockPanel.Dock="Bottom"
                                                                          Style="{DynamicResource {x:Static ExpanderKeys.ToggleRight}}">
                                                                    <Expander.Header>
                                                                        <DockPanel>
                                                                            <TextBlock>
                                                                                <Run Text="图像源 " />
                                                                            </TextBlock>
                                                                            <TextBlock>
                                                                                <b:Interaction.Behaviors>
                                                                                    <h:TextBlockIndexOfBebavior DefaultFromValue="1"
                                                                                                                Item="{Binding SrcFilePath}"
                                                                                                                Source="{Binding SrcFilePaths}" />
                                                                                </b:Interaction.Behaviors>
                                                                            </TextBlock>
                                                                        </DockPanel>
                                                                    </Expander.Header>
                                                                    <Cattach.CaptionRightTemplate>
                                                                        <ControlTemplate>
                                                                            <DockPanel HorizontalAlignment="Right">
                                                                                <FontIconButton Command="{Binding ClearImageDatasCommand}"
                                                                                                Content="{x:Static FontIcons.Delete}"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="清空" />
                                                                                <FontIconButton Command="{Binding DeleteImageDataCommand}"
                                                                                                Content="{x:Static FontIcons.Cancel}"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="删除" />
                                                                                <FontIconButton Command="{Binding AddImageDatasCommand}"
                                                                                                Content="{x:Static FontIcons.OpenFolderHorizontal}"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="添加文件夹" />
                                                                                <FontIconButton Command="{Binding AddImageDataCommand}"
                                                                                                Content="&#xF407;"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="添加文件" />
                                                                                <ToggleButton Height="Auto"
                                                                                              Margin="2,0"
                                                                                              Padding="5,3"
                                                                                              VerticalAlignment="Center"
                                                                                              Content="运行全部"
                                                                                              DockPanel.Dock="Right"
                                                                                              IsChecked="{Binding UseAllImage}" />
                                                                                <ToggleButton Height="Auto"
                                                                                              Padding="5,3"
                                                                                              VerticalAlignment="Center"
                                                                                              Content="自动切换"
                                                                                              DockPanel.Dock="Right"
                                                                                              IsChecked="{Binding UseAutoSwitch}" />
                                                                            </DockPanel>
                                                                        </ControlTemplate>
                                                                    </Cattach.CaptionRightTemplate>
                                                                    <ListBox Height="90"
                                                                             Cattach.ItemHeight="Auto"
                                                                             ItemsSource="{Binding SrcFilePaths}"
                                                                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                                                             ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                                                             SelectedItem="{Binding SrcFilePath}"
                                                                             Visibility="{Binding ElementName=tb_s, Path=IsChecked, Converter={x:Static Converter.GetTrueToVisible}}">
                                                                        <ItemsControl.ItemsPanel>
                                                                            <ItemsPanelTemplate>
                                                                                <VirtualizingStackPanel Orientation="Horizontal" />
                                                                            </ItemsPanelTemplate>
                                                                        </ItemsControl.ItemsPanel>
                                                                        <ItemsControl.Template>
                                                                            <ControlTemplate TargetType="ListBox">
                                                                                <Border Style="{DynamicResource S.Border.TemplatedParent}">
                                                                                    <Grid>
                                                                                        <ScrollViewer x:Name="sv"
                                                                                                      Focusable="false">
                                                                                            <b:Interaction.Behaviors>
                                                                                                <h:ScrollViewerBebavior UseHorizontalMouseWheel="True" />
                                                                                            </b:Interaction.Behaviors>
                                                                                            <ItemsPresenter Margin="{TemplateBinding Padding}"
                                                                                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                                                                        </ScrollViewer>
                                                                                        <FontIconButton Margin="5,0"
                                                                                                        HorizontalAlignment="Left"
                                                                                                        Command="{ScrollViewerPageLeftCommand}"
                                                                                                        CommandParameter="{Binding ElementName=sv}"
                                                                                                        Content="{x:Static FontIcons.PageLeft}"
                                                                                                        FontSize="25" />
                                                                                        <FontIconButton Margin="5,0"
                                                                                                        HorizontalAlignment="Right"
                                                                                                        Command="{ScrollViewerPageRightCommand}"
                                                                                                        CommandParameter="{Binding ElementName=sv}"
                                                                                                        Content="{x:Static FontIcons.PageRight}"
                                                                                                        FontSize="25" />
                                                                                    </Grid>
                                                                                </Border>
                                                                            </ControlTemplate>
                                                                        </ItemsControl.Template>
                                                                        <ItemsControl.ItemTemplate>
                                                                            <DataTemplate>
                                                                                <Border Width="75"
                                                                                        Height="75"
                                                                                        ToolTip="{Binding}">
                                                                                    <Image Source="{Binding ., Converter={GetImageSourceFromFilePathConverter}}" />
                                                                                </Border>
                                                                            </DataTemplate>
                                                                        </ItemsControl.ItemTemplate>
                                                                        <b:Interaction.Triggers>
                                                                            <b:EventTrigger EventName="SelectionChanged">
                                                                                <b:InvokeCommandAction Command="{Binding ElementName=dp_diagrameData, Path=DataContext.ImageFileSelectionChangedCommand}" />
                                                                            </b:EventTrigger>
                                                                            <b:EventTrigger EventName="MouseDoubleClick">
                                                                                <b:InvokeCommandAction Command="{ShowZoomViewImageFileCommand}"
                                                                                                       CommandParameter="{Binding SrcFilePath}" />
                                                                            </b:EventTrigger>
                                                                        </b:Interaction.Triggers>
                                                                    </ListBox>
                                                                </Expander>
                                                            </DataTemplate>
                                                            <DataTemplate DataType="{x:Type SrcVideoFilesNodeData}">
                                                                <Expander Cattach.GuideTitle="管理视频源"
                                                                          DockPanel.Dock="Bottom"
                                                                          Style="{DynamicResource {x:Static ExpanderKeys.ToggleRight}}">
                                                                    <Expander.Header>
                                                                        <DockPanel>
                                                                            <TextBlock>
                                                                                <Run Text="视频源 " />
                                                                            </TextBlock>
                                                                            <TextBlock>
                                                                                <b:Interaction.Behaviors>
                                                                                    <h:TextBlockIndexOfBebavior DefaultFromValue="1"
                                                                                                                Item="{Binding SrcFilePath}"
                                                                                                                Source="{Binding SrcFilePaths}" />
                                                                                </b:Interaction.Behaviors>
                                                                            </TextBlock>
                                                                        </DockPanel>
                                                                    </Expander.Header>
                                                                    <Cattach.CaptionRightTemplate>
                                                                        <ControlTemplate>
                                                                            <DockPanel HorizontalAlignment="Right">
                                                                                <FontIconButton Command="{Binding ClearImageDatasCommand}"
                                                                                                Content="{x:Static FontIcons.Delete}"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="清空" />
                                                                                <FontIconButton Command="{Binding DeleteImageDataCommand}"
                                                                                                Content="{x:Static FontIcons.Cancel}"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="删除" />
                                                                                <FontIconButton Command="{Binding AddImageDatasCommand}"
                                                                                                Content="{x:Static FontIcons.OpenFolderHorizontal}"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="添加文件夹" />
                                                                                <FontIconButton Command="{Binding AddImageDataCommand}"
                                                                                                Content="&#xF407;"
                                                                                                DockPanel.Dock="Right"
                                                                                                ToolTip="添加文件" />
                                                                                <ToggleButton Height="Auto"
                                                                                              Margin="2,0"
                                                                                              Padding="5,3"
                                                                                              VerticalAlignment="Center"
                                                                                              Content="运行全部"
                                                                                              DockPanel.Dock="Right"
                                                                                              IsChecked="{Binding UseAllImage}" />
                                                                                <ToggleButton Height="Auto"
                                                                                              Padding="5,3"
                                                                                              VerticalAlignment="Center"
                                                                                              Content="自动切换"
                                                                                              DockPanel.Dock="Right"
                                                                                              IsChecked="{Binding UseAutoSwitch}" />
                                                                            </DockPanel>
                                                                        </ControlTemplate>
                                                                    </Cattach.CaptionRightTemplate>
                                                                    <ListBox Height="80"
                                                                             Cattach.ItemHeight="Auto"
                                                                             ItemsSource="{Binding SrcFilePaths}"
                                                                             SelectedItem="{Binding SrcFilePath}"
                                                                             Visibility="{Binding ElementName=tb_s, Path=IsChecked, Converter={x:Static Converter.GetTrueToVisible}}">
                                                                        <ItemsControl.ItemsPanel>
                                                                            <ItemsPanelTemplate>
                                                                                <VirtualizingStackPanel Orientation="Horizontal" />
                                                                            </ItemsPanelTemplate>
                                                                        </ItemsControl.ItemsPanel>
                                                                        <ItemsControl.Template>
                                                                            <ControlTemplate TargetType="ListBox">
                                                                                <Border Style="{DynamicResource S.Border.TemplatedParent}">
                                                                                    <Grid>
                                                                                        <ScrollViewer x:Name="sv"
                                                                                                      Focusable="false">
                                                                                            <b:Interaction.Behaviors>
                                                                                                <h:ScrollViewerBebavior UseHorizontalMouseWheel="True" />
                                                                                            </b:Interaction.Behaviors>
                                                                                            <ItemsPresenter Margin="{TemplateBinding Padding}"
                                                                                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                                                                        </ScrollViewer>
                                                                                        <FontIconButton Margin="5,0"
                                                                                                        HorizontalAlignment="Left"
                                                                                                        Command="{ScrollViewerPageLeftCommand}"
                                                                                                        CommandParameter="{Binding ElementName=sv}"
                                                                                                        Content="{x:Static FontIcons.PageLeft}"
                                                                                                        FontSize="25" />
                                                                                        <FontIconButton Margin="5,0"
                                                                                                        HorizontalAlignment="Right"
                                                                                                        Command="{ScrollViewerPageRightCommand}"
                                                                                                        CommandParameter="{Binding ElementName=sv}"
                                                                                                        Content="{x:Static FontIcons.PageRight}"
                                                                                                        FontSize="25" />
                                                                                    </Grid>
                                                                                </Border>
                                                                            </ControlTemplate>
                                                                        </ItemsControl.Template>
                                                                        <ItemsControl.ItemTemplate>
                                                                            <DataTemplate>
                                                                                <DockPanel MaxWidth="200"
                                                                                           Margin="1">
                                                                                    <TextBlock VerticalAlignment="Center"
                                                                                               DockPanel.Dock="Bottom"
                                                                                               TextTrimming="CharacterEllipsis"
                                                                                               TextWrapping="Wrap"
                                                                                               ToolTip="{Binding .}">
                                                                                        <Run Text="[" /><Run Text="{Binding ., Converter={GetFilePathSizeToDisplayConverter}}" /><Run Text="]" />
                                                                                        <Run Text="{Binding ., Converter={x:Static Converter.GetFileName}}" />
                                                                                    </TextBlock>
                                                                                    <Image Height="40"
                                                                                           Margin="5"
                                                                                           Source="{Binding ., Converter={h:GetFilePathToSystemInfoIconConverter}}" />
                                                                                </DockPanel>
                                                                            </DataTemplate>
                                                                        </ItemsControl.ItemTemplate>
                                                                        <b:Interaction.Triggers>
                                                                            <b:EventTrigger EventName="SelectionChanged">
                                                                                <b:InvokeCommandAction Command="{Binding ElementName=dp_diagrameData, Path=DataContext.ImageFileSelectionChangedCommand}" />
                                                                            </b:EventTrigger>
                                                                            <b:EventTrigger EventName="MouseDoubleClick">
                                                                                <b:InvokeCommandAction Command="{ShowZoomViewImageFileCommand}"
                                                                                                       CommandParameter="{Binding SrcFilePath}" />
                                                                            </b:EventTrigger>
                                                                        </b:Interaction.Triggers>
                                                                    </ListBox>
                                                                </Expander>
                                                            </DataTemplate>
                                                            <DataTemplate DataType="{x:Type CameraCaptureNodeData}" />
                                                        </ContentPresenter.Resources>
                                                    </ContentPresenter>

                                                    <TabControl Cattach.CaptionHorizontalAlignment="Center"
                                                                ItemContainerStyle="{DynamicResource {x:Static TabItemKeys.Line}}"
                                                                SelectedIndex="{Binding SelectedImageTabIndex}">
                                                        <TabItem MinWidth="100"
                                                                 HorizontalContentAlignment="Center"
                                                                 Header="图像"
                                                                 ToolTip="当前选择图像源、节点、历史结果或正在运行的节点结果">
                                                            <Grid>
                                                                <h:Zoombox x:Name="zoomview1"
                                                                           Background="{DynamicResource {x:Static BrushKeys.Tile25}}"
                                                                           DragModifiers=""
                                                                           Focusable="True"
                                                                           IsTabStop="True"
                                                                           MinScale="0.1"
                                                                           NavigateOnPreview="False"
                                                                           RelativeZoomModifiers=""
                                                                           Tag="Image"
                                                                           ViewStackIndex="0"
                                                                           ZoomOn="Content">
                                                                    <b:Interaction.Behaviors>
                                                                        <h:ZoomBoxFitOnLoadedBehavior />
                                                                        <h:ZoomBoxFitOnSizeChangedBehavior />
                                                                    </b:Interaction.Behaviors>
                                                                    <h:Zoombox.ViewStack>
                                                                        <h:ZoomboxView>Fit</h:ZoomboxView>
                                                                    </h:Zoombox.ViewStack>
                                                                    <b:Interaction.Triggers>
                                                                        <h:MouseTrigger ClickCount="2"
                                                                                        Mode="Right"
                                                                                        MouseButton="Left"
                                                                                        UseHandle="False">
                                                                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                                                                  TargetObject="{Binding ElementName=zoomview1}" />
                                                                        </h:MouseTrigger>
                                                                        <h:MouseTrigger ClickCount="2"
                                                                                        Mode="Left"
                                                                                        MouseButton="Left"
                                                                                        UseHandle="False">
                                                                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                                                                  TargetObject="{Binding ElementName=zoomview1}" />
                                                                        </h:MouseTrigger>
                                                                        <b:EventTrigger EventName="MouseDoubleClick">
                                                                            <b:InvokeCommandAction Command="{ShowZoomViewImageCommand}"
                                                                                                   CommandParameter="{Binding ResultImageSource}" />
                                                                        </b:EventTrigger>
                                                                    </b:Interaction.Triggers>
                                                                    <Image MinWidth="100"
                                                                           MinHeight="100">
                                                                        <b:Interaction.Triggers>
                                                                            <b:EventTrigger EventName="SourceUpdated">
                                                                                <b:InvokeCommandAction Command="{Binding ImageSourceUpdatedCommand}"
                                                                                                       CommandParameter="{Binding ElementName=zoomview1}" />
                                                                                <!--<h:CallMethodActionEx MethodName="FitToBounds"
                                                                                                      TargetObject="{Binding ElementName=zoomview1}" />-->
                                                                            </b:EventTrigger>
                                                                            <b:EventTrigger EventName="TargetUpdated">
                                                                                <b:InvokeCommandAction Command="{Binding ImageSourceUpdatedCommand}"
                                                                                                       CommandParameter="{Binding ElementName=zoomview1}" />
                                                                                <!--<h:CallMethodActionEx MethodName="FitToBounds"
                                                                                                      TargetObject="{Binding ElementName=zoomview1}" />-->
                                                                            </b:EventTrigger>
                                                                        </b:Interaction.Triggers>
                                                                        <Image.Style>
                                                                            <Style TargetType="Image">
                                                                                <Setter Property="Source" Value="{Binding ResultImageSource, NotifyOnSourceUpdated=True, NotifyOnTargetUpdated=True}" />
                                                                                <Style.Triggers>
                                                                                    <DataTrigger Binding="{Binding ResultImageSource}"
                                                                                                 Value="{x:Null}">
                                                                                        <Setter Property="Source" Value="{Binding SelectedImageData.FilePath}" />
                                                                                    </DataTrigger>
                                                                                </Style.Triggers>
                                                                            </Style>
                                                                        </Image.Style>
                                                                    </Image>
                                                                </h:Zoombox>
                                                                <Border Margin="5"
                                                                        HorizontalAlignment="Left"
                                                                        VerticalAlignment="Top"
                                                                        Style="{DynamicResource {x:Static BorderKeys.Default}}">
                                                                    <TextBlock Margin="10,6"
                                                                               HorizontalAlignment="Center"
                                                                               VerticalAlignment="Center">
                                                                        <TextBlock.Style>
                                                                            <Style TargetType="TextBlock">
                                                                                <Setter Property="Visibility" Value="Visible" />
                                                                                <Setter Property="Text" Value="{Binding ResultType}" />
                                                                                <Style.Triggers>
                                                                                    <DataTrigger Binding="{Binding ResultImageSource}"
                                                                                                 Value="{x:Null}">
                                                                                        <Setter Property="Visibility" Value="Visible" />
                                                                                        <Setter Property="Text" Value="无结果" />
                                                                                    </DataTrigger>
                                                                                </Style.Triggers>
                                                                            </Style>
                                                                        </TextBlock.Style>
                                                                    </TextBlock>
                                                                </Border>

                                                                <Border Padding="10,6"
                                                                        VerticalAlignment="Bottom">
                                                                    <Border.Background>
                                                                        <SolidColorBrush Opacity="0.3"
                                                                                         Color="Black" />
                                                                    </Border.Background>
                                                                    <DockPanel VerticalAlignment="Center"
                                                                               TextBlock.Foreground="LightGray">

                                                                        <TextBlock DockPanel.Dock="Right">
                                                                            <Run Text="{Binding SrcImageNodeData.SrcFilePath, Converter={x:Static h:ConverterEx.GetImagePixelDisplay}}" />
                                                                            <Run Text="|" />
                                                                            <Run Text="{Binding SrcImageNodeData.SrcFilePath, Converter={h:GetFilePathSizeToDisplayConverter}}" />
                                                                            <Run Text="|" />
                                                                            <Run Text="{Binding SrcImageNodeData.SrcFilePath, Converter={x:Static h:Converter.GetFileLastAccessTime}}" />
                                                                        </TextBlock>
                                                                        <TextBlock Text="{Binding SrcImageNodeData.SrcFilePath, Converter={x:Static h:Converter.GetFileName}}"
                                                                                   TextTrimming="CharacterEllipsis"
                                                                                   ToolTip="{Binding SrcImageNodeData.SrcFilePath}" />
                                                                    </DockPanel>
                                                                    <Border.Style>
                                                                        <Style TargetType="Border">
                                                                            <Style.Triggers>
                                                                                <DataTrigger Binding="{Binding SrcImageNodeData.SrcFilePath}"
                                                                                             Value="{x:Null}">
                                                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding SrcImageNodeData}"
                                                                                             Value="{x:Null}">
                                                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                                                </DataTrigger>
                                                                            </Style.Triggers>
                                                                        </Style>
                                                                    </Border.Style>
                                                                </Border>
                                                            </Grid>
                                                        </TabItem>
                                                        <TabItem MinWidth="100"
                                                                 HorizontalContentAlignment="Center"
                                                                 Header="模块结果"
                                                                 ToolTip="当前选择图像源、节点、历史结果或正在运行的节点结果">
                                                            <GroupBox TextBlock.Foreground="{DynamicResource {x:Static BrushKeys.Foreground}}">
                                                                <GroupBox.Header>
                                                                    <TextBlock>
                                                                        <Run Text="模块名称&lt;" />
                                                                        <Run Text="{Binding ResultNodeData.Name}" />
                                                                        <Run Text="&gt;" />
                                                                    </TextBlock>
                                                                </GroupBox.Header>
                                                                <Form SelectObject="{Binding ResultNodeData}"
                                                                      UseGroupNames="{x:Static VisionPropertyGroupNames.ResultParameters}" />
                                                            </GroupBox>
                                                        </TabItem>
                                                    </TabControl>
                                                </DockPanel>
                                            </DataTemplate>
                                            <!--<DataTemplate DataType="{x:Type ld:VisionVedioOpenCVDiagramData}">
                                            <DockPanel>
                                                <Expander Cattach.GuideTitle="管理视频源"
                                                          DockPanel.Dock="Bottom"
                                                          Style="{DynamicResource {x:Static ExpanderKeys.ToggleRight}}">
                                                    <Expander.Header>
                                                        <DockPanel>
                                                            <TextBlock>
                                                                <Run Text="视频源 " />
                                                            </TextBlock>
                                                            <TextBlock>
                                                                <b:Interaction.Behaviors>
                                                                    <h:TextBlockIndexOfBebavior DefaultFromValue="1"
                                                                                                Item="{Binding SelectedImageData}"
                                                                                                Source="{Binding ImageDatas}" />
                                                                </b:Interaction.Behaviors>
                                                            </TextBlock>
                                                        </DockPanel>
                                                    </Expander.Header>
                                                    <Cattach.CaptionRightTemplate>
                                                        <ControlTemplate>
                                                            <DockPanel HorizontalAlignment="Right">
                                                                <FontIconButton Command="{Binding ClearImageDatasCommand}"
                                                                                Content="{x:Static FontIcons.Delete}"
                                                                                DockPanel.Dock="Right"
                                                                                ToolTip="清空" />
                                                                <FontIconButton Command="{Binding DeleteImageDataCommand}"
                                                                                Content="{x:Static FontIcons.Cancel}"
                                                                                DockPanel.Dock="Right"
                                                                                ToolTip="删除" />
                                                                <FontIconButton Command="{Binding AddImageDatasCommand}"
                                                                                Content="{x:Static FontIcons.OpenFolderHorizontal}"
                                                                                DockPanel.Dock="Right"
                                                                                ToolTip="添加文件夹" />
                                                                <FontIconButton Command="{Binding AddImageDataCommand}"
                                                                                Content="&#xF407;"
                                                                                DockPanel.Dock="Right"
                                                                                ToolTip="添加文件" />
                                                                <ToggleButton Height="Auto"
                                                                              Margin="2,0"
                                                                              Padding="5,3"
                                                                              VerticalAlignment="Center"
                                                                              Content="运行全部"
                                                                              DockPanel.Dock="Right"
                                                                              IsChecked="{Binding UseAllImage}" />
                                                                <ToggleButton Height="Auto"
                                                                              Padding="5,3"
                                                                              VerticalAlignment="Center"
                                                                              Content="自动切换"
                                                                              DockPanel.Dock="Right"
                                                                              IsChecked="{Binding UseAutoSwitch}" />
                                                            </DockPanel>
                                                        </ControlTemplate>
                                                    </Cattach.CaptionRightTemplate>
                                                    <ListBox Height="80"
                                                             Cattach.ItemHeight="Auto"
                                                             ItemsSource="{Binding ImageDatas, IsAsync=True}"
                                                             SelectedItem="{Binding SelectedImageData, IsAsync=True}"
                                                             Visibility="{Binding ElementName=tb_s, Path=IsChecked, Converter={x:Static Converter.GetTrueToVisible}}">
                                                        <ItemsControl.ItemsPanel>
                                                            <ItemsPanelTemplate>
                                                                <VirtualizingStackPanel />
                                                            </ItemsPanelTemplate>
                                                        </ItemsControl.ItemsPanel>
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <DockPanel>
                                                                    <Image Height="25"
                                                                           Margin="5,0"
                                                                           Source="{Binding FilePath, Converter={h:GetFilePathToSystemInfoIconConverter}}" />
                                                                    <TextBlock VerticalAlignment="Center"
                                                                               DockPanel.Dock="Right"
                                                                               Text="{Binding FilePath, Converter={GetFilePathSizeToDisplayConverter}}" />
                                                                    <TextBlock VerticalAlignment="Center"
                                                                               Text="{Binding FilePath, Converter={x:Static Converter.GetFileName}}"
                                                                               ToolTip="{Binding FilePath}" />
                                                                </DockPanel>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                        <b:Interaction.Triggers>
                                                            <b:EventTrigger EventName="SelectionChanged">
                                                                <b:InvokeCommandAction Command="{Binding ImageFileSelectionChangedCommand}" />
                                                            </b:EventTrigger>
                                                            <b:EventTrigger EventName="MouseDoubleClick">
                                                                <b:InvokeCommandAction Command="{ShowZoomViewImageFileCommand}"
                                                                                       CommandParameter="{Binding SelectedImageData.FilePath}" />
                                                            </b:EventTrigger>
                                                        </b:Interaction.Triggers>
                                                    </ListBox>
                                                </Expander>
                                                <TabControl Cattach.CaptionHorizontalAlignment="Center"
                                                            ItemContainerStyle="{DynamicResource {x:Static TabItemKeys.Line}}"
                                                            SelectedIndex="{Binding SelectedImageTabIndex}">
                                                    <TabItem MinWidth="100"
                                                             Cattach.GuideAssemblyVersion="*******"
                                                             Header="视频"
                                                             ToolTip="当前选择视频源视图">
                                                        <Grid>
                                                            <Image Margin="20"
                                                                   Source="{Binding SelectedImageData.FilePath, Converter={GetFilePathToSystemInfoIconConverter}}" />
                                                            <Border Padding="10,6"
                                                                    VerticalAlignment="Bottom">
                                                                <Border.Background>
                                                                    <SolidColorBrush Opacity="0.3"
                                                                                     Color="Black" />
                                                                </Border.Background>
                                                                <DockPanel VerticalAlignment="Center"
                                                                           TextBlock.Foreground="White">
                                                                    <TextBlock DockPanel.Dock="Right">
                                                                        <Run Text="{Binding SelectedImageData.FilePath, Converter={h:GetFilePathSizeToDisplayConverter}}" />
                                                                        <Run Text="|" />
                                                                        <Run Text="{Binding SelectedImageData.FilePath, Converter={x:Static h:Converter.GetFileLastAccessTime}}" />
                                                                    </TextBlock>
                                                                    <TextBlock Text="{Binding SelectedImageData.FilePath, Converter={x:Static h:Converter.GetFileName}}"
                                                                               TextTrimming="CharacterEllipsis"
                                                                               TextWrapping="Wrap"
                                                                               ToolTip="{Binding SelectedImageData.FilePath}" />
                                                                </DockPanel>
                                                            </Border>
                                                            <Border Padding="10,6"
                                                                    VerticalAlignment="Top">
                                                                <Border.Background>
                                                                    <SolidColorBrush Opacity="0.3"
                                                                                     Color="Black" />
                                                                </Border.Background>
                                                                <DockPanel VerticalAlignment="Center"
                                                                           LastChildFill="False"
                                                                           TextBlock.Foreground="White">
                                                                    <TextBlock Text="{Binding SelectedImageData.FilePath}"
                                                                               TextWrapping="Wrap"
                                                                               ToolTip="{Binding SelectedImageData.FilePath}" />
                                                                </DockPanel>
                                                            </Border>
                                                        </Grid>
                                                    </TabItem>
                                                    <TabItem MinWidth="100"
                                                             HorizontalContentAlignment="Center"
                                                             Cattach.GuideAssemblyVersion="*******"
                                                             Header="模块结果"
                                                             ToolTip="当前选择图像源、节点、历史结果或正在运行的节点结果">
                                                        <Grid>
                                                            <h:Zoombox x:Name="zoomview1"
                                                                       Background="{DynamicResource {x:Static BrushKeys.Tile25}}"
                                                                       DragModifiers=""
                                                                       Focusable="True"
                                                                       IsTabStop="True"
                                                                       MinScale="0.2"
                                                                       NavigateOnPreview="False"
                                                                       RelativeZoomModifiers=""
                                                                       ViewStackIndex="0"
                                                                       ZoomOn="Content">
                                                                <b:Interaction.Behaviors>
                                                                    <h:ZoomBoxFitOnLoadedBehavior />
                                                                    <h:ZoomBoxFitOnSizeChangedBehavior />
                                                                </b:Interaction.Behaviors>
                                                                <h:Zoombox.ViewStack>
                                                                    <h:ZoomboxView>Fit</h:ZoomboxView>
                                                                </h:Zoombox.ViewStack>
                                                                <b:Interaction.Triggers>
                                                                    <h:MouseTrigger ClickCount="2"
                                                                                    Mode="Right"
                                                                                    MouseButton="Left"
                                                                                    UseHandle="False">
                                                                        <h:CallMethodActionEx MethodName="FitToBounds"
                                                                                              TargetObject="{Binding ElementName=zoomview1}" />
                                                                    </h:MouseTrigger>
                                                                    <h:MouseTrigger ClickCount="2"
                                                                                    Mode="Left"
                                                                                    MouseButton="Left"
                                                                                    UseHandle="False">
                                                                        <h:CallMethodActionEx MethodName="FitToBounds"
                                                                                              TargetObject="{Binding ElementName=zoomview1}" />
                                                                    </h:MouseTrigger>
                                                                    <b:EventTrigger EventName="MouseDoubleClick">
                                                                        <b:InvokeCommandAction Command="{ShowZoomViewImageCommand}"
                                                                                               CommandParameter="{Binding ResultImageSource}" />
                                                                    </b:EventTrigger>
                                                                </b:Interaction.Triggers>
                                                                <Image MinWidth="100"
                                                                       MinHeight="100"
                                                                       Source="{Binding ResultImageSource, NotifyOnSourceUpdated=True, NotifyOnTargetUpdated=True}">
                                                                    <b:Interaction.Triggers>
                                                                        <b:EventTrigger EventName="SourceUpdated">
                                                                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                                                                  TargetObject="{Binding ElementName=zoomview1}" />
                                                                        </b:EventTrigger>
                                                                        <b:EventTrigger EventName="TargetUpdated">
                                                                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                                                                  TargetObject="{Binding ElementName=zoomview1}" />
                                                                        </b:EventTrigger>
                                                                    </b:Interaction.Triggers>
                                                                </Image>
                                                            </h:Zoombox>
                                                            <Border Margin="5"
                                                                    HorizontalAlignment="Left"
                                                                    VerticalAlignment="Top"
                                                                    Style="{DynamicResource {x:Static BorderKeys.Default}}">
                                                                <TextBlock Margin="10,6"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Center">
                                                                    <TextBlock.Style>
                                                                        <Style TargetType="TextBlock">
                                                                            <Setter Property="Visibility" Value="Visible" />
                                                                            <Setter Property="Text" Value="{Binding ResultType}" />
                                                                            <Style.Triggers>
                                                                                <DataTrigger Binding="{Binding ResultImageSource}"
                                                                                             Value="{x:Null}">
                                                                                    <Setter Property="Visibility" Value="Visible" />
                                                                                    <Setter Property="Text" Value="无结果" />
                                                                                </DataTrigger>
                                                                            </Style.Triggers>
                                                                        </Style>
                                                                    </TextBlock.Style>
                                                                </TextBlock>
                                                            </Border>
                                                        </Grid>
                                                    </TabItem>
                                                </TabControl>
                                            </DockPanel>
                                        </DataTemplate>-->

                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                    <GridSplitter Grid.Row="1"
                                                  Style="{DynamicResource {x:Static GridSplitterKeys.Horizontal}}" />
                                    <TabControl x:Name="tc"
                                                Grid.Row="4"
                                                VerticalAlignment="Stretch"
                                                BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                                Cattach.CaptionHorizontalAlignment="Center"
                                                ItemContainerStyle="{DynamicResource {x:Static TabItemKeys.Line}}">
                                        <TabControl.Template>
                                            <ControlTemplate TargetType="{x:Type TabControl}">
                                                <Border Style="{DynamicResource S.Border.TemplatedParent}">
                                                    <Grid ClipToBounds="true"
                                                          KeyboardNavigation.TabNavigation="Local"
                                                          SnapsToDevicePixels="true">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition x:Name="ColumnDefinition0" />
                                                            <ColumnDefinition x:Name="ColumnDefinition1"
                                                                              Width="0" />
                                                        </Grid.ColumnDefinitions>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition x:Name="RowDefinition0"
                                                                           Height="Auto" />
                                                            <RowDefinition x:Name="RowDefinition1"
                                                                           Height="*" />
                                                        </Grid.RowDefinitions>
                                                        <ScrollViewer x:Name="headerPanel"
                                                                      Margin="0,0,0,1"
                                                                      VerticalScrollBarVisibility="Auto">
                                                            <DockPanel>
                                                                <FontIconToggleButton x:Name="tb"
                                                                                      Cattach.GuideTitle="显示隐藏结果菜单页面"
                                                                                      Cattach.UseGuide="True"
                                                                                      CheckedGlyph="&#xE018;"
                                                                                      DockPanel.Dock="Right"
                                                                                      IsChecked="True"
                                                                                      Style="{DynamicResource {x:Static FontIconToggleButtonKeys.Switch}}"
                                                                                      UncheckedGlyph="&#xE019;">
                                                                    <b:Interaction.Triggers>
                                                                        <b:EventTrigger EventName="Unchecked">
                                                                            <b:ChangePropertyAction PropertyName="Height"
                                                                                                    TargetObject="{Binding ElementName=r_d}"
                                                                                                    Value="10" />
                                                                        </b:EventTrigger>
                                                                        <b:EventTrigger EventName="Checked">
                                                                            <b:ChangePropertyAction PropertyName="Height"
                                                                                                    TargetObject="{Binding ElementName=r_d}"
                                                                                                    Value="200" />
                                                                        </b:EventTrigger>
                                                                    </b:Interaction.Triggers>
                                                                </FontIconToggleButton>
                                                                <TabPanel Grid.Row="0"
                                                                          Grid.Column="0"
                                                                          Panel.ZIndex="1"
                                                                          Background="Transparent"
                                                                          IsItemsHost="true"
                                                                          KeyboardNavigation.TabIndex="1" />
                                                            </DockPanel>
                                                        </ScrollViewer>
                                                        <Border x:Name="contentPanel"
                                                                Grid.Row="1"
                                                                Grid.Column="0"
                                                                KeyboardNavigation.DirectionalNavigation="Contained"
                                                                KeyboardNavigation.TabIndex="2"
                                                                KeyboardNavigation.TabNavigation="Local">
                                                            <ContentControl x:Name="PART_SelectedContentHost"
                                                                            Margin="{TemplateBinding Padding}"
                                                                            Content="{TemplateBinding SelectedContent}"
                                                                            ContentTemplate="{TemplateBinding SelectedContentTemplate}"
                                                                            Foreground="{TemplateBinding Foreground}"
                                                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                                        </Border>
                                                    </Grid>
                                                </Border>
                                            </ControlTemplate>
                                        </TabControl.Template>

                                        <TabItem MinWidth="100"
                                                 Header="历史结果"
                                                 ToolTip="当前流程图运行的消息结果">
                                            <DataGrid AutoGenerateColumns="False"
                                                      IsReadOnly="True"
                                                      ItemsSource="{Binding SelectedDiagramData.Messages}">
                                                <b:Interaction.Triggers>
                                                    <b:EventTrigger EventName="SelectionChanged">
                                                        <b:InvokeCommandAction Command="{Binding SelectedDiagramData.SelectedMessageChangedCommand}"
                                                                               PassEventArgsToCommand="True" />
                                                    </b:EventTrigger>
                                                </b:Interaction.Triggers>
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn Width="Auto"
                                                                        Binding="{Binding Index}"
                                                                        Header="执行序号" />
                                                    <DataGridTextColumn Width="Auto"
                                                                        MinWidth="90"
                                                                        Binding="{Binding TimeSpan}"
                                                                        Header="执行时间" />
                                                    <DataGridTextColumn Width="*"
                                                                        Binding="{Binding Type}"
                                                                        Header="模块" />
                                                    <DataGridTemplateColumn Width="2*"
                                                                            Header="结果数据">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                            <DataTemplate>
                                                                <DockPanel>
                                                                    <FontIconTextBlock x:Name="txt"
                                                                                       Margin="5,0"
                                                                                       Text="{x:Static FontIcons.Info}" />
                                                                    <TextBlock VerticalAlignment="Center"
                                                                               TextWrapping="Wrap"
                                                                               ToolTip="{Binding Message}">
                                                                        <Run Text="{Binding Message}" />
                                                                    </TextBlock>
                                                                </DockPanel>
                                                                <DataTemplate.Triggers>
                                                                    <DataTrigger Binding="{Binding State}"
                                                                                 Value="Error">
                                                                        <Setter TargetName="txt" Property="Foreground" Value="{DynamicResource {x:Static BrushKeys.Red}}" />
                                                                        <Setter TargetName="txt" Property="Text" Value="{x:Static FontIcons.Error}" />
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding State}"
                                                                                 Value="Success">
                                                                        <Setter TargetName="txt" Property="Foreground" Value="{DynamicResource {x:Static BrushKeys.Green}}" />
                                                                        <Setter TargetName="txt" Property="Text" Value="{x:Static FontIcons.Completed}" />
                                                                    </DataTrigger>
                                                                </DataTemplate.Triggers>
                                                            </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                    </DataGridTemplateColumn>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </TabItem>
                                        <TabItem MinWidth="100"
                                                 Header="当前模块结果">
                                            <ContentPresenter Content="{Binding SelectedDiagramData.ResultNodeData.ResultPresenter}">
                                                <b:Interaction.Triggers>
                                                    <b:EventTrigger EventName="PreviewMouseLeftButtonDown">
                                                        <b:InvokeCommandAction Command="{lc:ZoomToRectCommand}"
                                                                               PassEventArgsToCommand="True" />
                                                    </b:EventTrigger>
                                                    <b:EventTrigger EventName="(DataGrids.SelectionChanged)">
                                                        <b:InvokeCommandAction Command="{lc:ZoomToRectCommand}"
                                                                               PassEventArgsToCommand="True" />
                                                    </b:EventTrigger>
                                                </b:Interaction.Triggers>
                                            </ContentPresenter>
                                        </TabItem>
                                        <TabItem MinWidth="100"
                                                 Header="帮助">
                                            <GroupBox TextBlock.Foreground="{DynamicResource {x:Static BrushKeys.Foreground}}">
                                                <GroupBox.Header>
                                                    <TextBlock>
                                                        <Run Text="模块名称 &lt;" />
                                                        <Run Text="{Binding SelectedDiagramData.ResultNodeData.Name}" />
                                                        <Run Text="&gt;" />
                                                    </TextBlock>
                                                </GroupBox.Header>
                                                <DockPanel>
                                                    <ContentPresenter HorizontalAlignment="Left"
                                                                      Content="{Binding SelectedDiagramData.ResultNodeData}"
                                                                      DockPanel.Dock="Top" />
                                                    <TextBlock Margin="5"
                                                               DockPanel.Dock="Top">
                                                        <Run Text="功能:" />
                                                        <Run Text="{Binding SelectedDiagramData.ResultNodeData.Description}" />
                                                    </TextBlock>
                                                    <ContentPresenter Content="{Binding SelectedDiagramData.ResultNodeData.HelpPresenter}" />
                                                </DockPanel>
                                            </GroupBox>
                                        </TabItem>
                                    </TabControl>
                                </Grid>
                            </DockPanel>

                        </h:GridSplitterBox.MenuContent>
                        <DockPanel>
                            <StatusBar BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                       BorderThickness="0,1,0,0"
                                       Cattach.GuideTitle="流程图消息"
                                       Cattach.UseGuide="True"
                                       DockPanel.Dock="Bottom">
                                <FontIconTextBlock>
                                    <FontIconTextBlock.Style>
                                        <Style TargetType="FontIconTextBlock">
                                            <Setter Property="Text" Value="&#xEC58;" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SelectedDiagramData.State}"
                                                             Value="Error">
                                                    <Setter Property="Text" Value="{x:Static FontIcons.Error}" />
                                                    <Setter Property="Foreground" Value="{DynamicResource {x:Static BrushKeys.Red}}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedDiagramData.State}"
                                                             Value="Success">
                                                    <Setter Property="Text" Value="{x:Static FontIcons.Completed}" />
                                                    <Setter Property="Foreground" Value="{DynamicResource {x:Static BrushKeys.Green}}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedDiagramData.State}"
                                                             Value="Running">
                                                    <Setter Property="Text" Value="&#xEC58;" />
                                                    <Setter Property="Foreground" Value="{DynamicResource {x:Static BrushKeys.Accent}}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </FontIconTextBlock.Style>
                                </FontIconTextBlock>
                                <TextBlock>
                                    <Run Text="{Binding SelectedDiagramData.State}" />
                                    <Run Text="{Binding SelectedDiagramData.CurrentMessage.Message}" />
                                    <Run Text="用时:" />
                                    <Run Text="{Binding SelectedDiagramData.CurrentMessage.TimeSpan, Converter={x:Static h:Converter.GetTimeSpanStr}, FallbackValue=00:00:00}" />
                                </TextBlock>

                                <TextBlock HorizontalAlignment="Right"
                                           VerticalAlignment="Bottom">
                                    <Hyperlink Command="{ProcessCommand}"
                                               CommandParameter="https://www.bilibili.com/video/BV1smd5YYEoh/?vd_source=d056512191c083233a4da171dd62475c">
                                        下载地址
                                    </Hyperlink>
                                </TextBlock>
                            </StatusBar>
                            <TabControl ItemsSource="{Binding DiagramDatas}"
                                        SelectedItem="{Binding SelectedDiagramData}">
                                <TabControl.ItemTemplate>
                                    <DataTemplate>
                                        <DockPanel>
                                            <FontIconTextBlock Text="{x:Static FontIcons.Photo2}" />
                                            <FontIconButton Width="Auto"
                                                            Command="{Binding ResetCommand}"
                                                            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TabControl}}"
                                                            Content="&#xE1CD;"
                                                            DockPanel.Dock="Right"
                                                            ToolTip="重置"
                                                            Visibility="{Binding RelativeSource={RelativeSource AncestorType=TabItem}, Path=IsSelected, Converter={h:GetTrueToVisibleConverter}}" />
                                            <FontIconButton Width="Auto"
                                                            Command="{Binding StopCommand}"
                                                            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TabControl}}"
                                                            Content="&#xE1D2;"
                                                            DockPanel.Dock="Right"
                                                            ToolTip="停止"
                                                            Visibility="{Binding RelativeSource={RelativeSource AncestorType=TabItem}, Path=IsSelected, Converter={h:GetTrueToVisibleConverter}}" />
                                            <FontIconButton Width="Auto"
                                                            Command="{Binding StartCommand}"
                                                            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=TabControl}}"
                                                            Content="{x:Static FontIcons.Replay}"
                                                            DockPanel.Dock="Right"
                                                            ToolTip="启动"
                                                            Visibility="{Binding RelativeSource={RelativeSource AncestorType=TabItem}, Path=IsSelected, Converter={h:GetTrueToVisibleConverter}}" />
                                            <TextBox Height="Auto"
                                                     MinWidth="20"
                                                     Margin="2,0"
                                                     Padding="0,2"
                                                     VerticalAlignment="Center"
                                                     VerticalContentAlignment="Center"
                                                     Style="{DynamicResource {x:Static TextBoxKeys.Edit}}"
                                                     Text="{Binding Name}">
                                                <TextBox.ContextMenu>
                                                    <ContextMenu ItemsSource="{Binding Source={x:Static IocProject.Instance}, Path=Current.Commands}">
                                                        <ItemsControl.ItemContainerStyle>
                                                            <Style BasedOn="{StaticResource {x:Static MenuItemKeys.Default}}"
                                                                   TargetType="MenuItem">
                                                                <Setter Property="Header" Value="{Binding Name}" />
                                                                <Setter Property="Cattach.Icon" Value="{Binding Icon}" />
                                                                <Setter Property="Command" Value="{Binding}" />
                                                            </Style>
                                                        </ItemsControl.ItemContainerStyle>
                                                    </ContextMenu>
                                                </TextBox.ContextMenu>
                                                <b:Interaction.Behaviors>
                                                    <h:TextBoxEditOnDoubleClickBebavior />
                                                </b:Interaction.Behaviors>
                                            </TextBox>
                                        </DockPanel>
                                    </DataTemplate>
                                </TabControl.ItemTemplate>
                                <TabControl.Template>
                                    <ControlTemplate TargetType="{x:Type TabControl}">
                                        <Border Style="{DynamicResource S.Border.TemplatedParent}">
                                            <Grid ClipToBounds="true"
                                                  KeyboardNavigation.TabNavigation="Local"
                                                  SnapsToDevicePixels="true">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition x:Name="ColumnDefinition0" />
                                                    <ColumnDefinition x:Name="ColumnDefinition1"
                                                                      Width="0" />
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition x:Name="RowDefinition0"
                                                                   Height="Auto" />
                                                    <RowDefinition x:Name="RowDefinition1"
                                                                   Height="*" />
                                                </Grid.RowDefinitions>
                                                <ScrollViewer x:Name="headerPanel"
                                                              Margin="0,0,0,1"
                                                              VerticalScrollBarVisibility="Auto">
                                                    <DockPanel LastChildFill="False">
                                                        <FontIconTextBlock Margin="10,0"
                                                                           Foreground="{DynamicResource {x:Static BrushKeys.Green}}"
                                                                           Text="&#xF003;" />
                                                        <TabPanel Grid.Row="0"
                                                                  Grid.Column="0"
                                                                  Panel.ZIndex="1"
                                                                  Background="Transparent"
                                                                  IsItemsHost="true"
                                                                  KeyboardNavigation.TabIndex="1" />
                                                        <FontIconButton Command="{Binding AddDiagramCommand}"
                                                                        Content="&#xE710;"
                                                                        SnapsToDevicePixels="True"
                                                                        ToolTip="添加流程图" />
                                                    </DockPanel>
                                                </ScrollViewer>

                                                <ListBox Grid.Row="1"
                                                         Grid.Column="0"
                                                         Background="{x:Null}"
                                                         BorderBrush="{x:Null}"
                                                         ItemsSource="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=ItemsSource}"
                                                         ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                                         ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                                         SelectedItem="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=SelectedItem}">
                                                    <ItemsControl.ItemsPanel>
                                                        <ItemsPanelTemplate>
                                                            <Grid />
                                                        </ItemsPanelTemplate>
                                                    </ItemsControl.ItemsPanel>
                                                    <ItemsControl.ItemContainerStyle>
                                                        <Style TargetType="ListBoxItem">
                                                            <Setter Property="Visibility" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=IsSelected, Converter={x:Static Converter.GetTrueToVisible}}" />
                                                            <Setter Property="ContentTemplate" Value="{Binding RelativeSource={RelativeSource AncestorType=TabControl}, Path=ContentTemplate}" />
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="ListBoxItem">
                                                                        <ContentPresenter Content="{TemplateBinding Content}"
                                                                                          ContentTemplate="{TemplateBinding ContentTemplate}" />
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </ItemsControl.ItemContainerStyle>
                                                </ListBox>
                                                <!--<Border x:Name="contentPanel"
                                     Grid.Row="1"
                                     Grid.Column="0"
                                     KeyboardNavigation.DirectionalNavigation="Contained"
                                     KeyboardNavigation.TabIndex="2"
                                     KeyboardNavigation.TabNavigation="Local">
                                 <ContentControl x:Name="PART_SelectedContentHost"
                                                 Margin="{TemplateBinding Padding}"
                                                 Content="{TemplateBinding SelectedContent}"
                                                 ContentTemplate="{TemplateBinding SelectedContentTemplate}"
                                                 Foreground="{TemplateBinding Foreground}"
                                                 SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                             </Border>-->
                                            </Grid>
                                        </Border>
                                    </ControlTemplate>
                                </TabControl.Template>
                                <TabControl.ContentTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                                BorderThickness="0,1,0,0">
                                            <h:Zoombox x:Name="zoomview"
                                                       DragModifiers=""
                                                       Focusable="True"
                                                       IsTabStop="True"
                                                       MinScale="0.2"
                                                       NavigateOnPreview="False"
                                                       RelativeZoomModifiers=""
                                                       ViewStackIndex="0"
                                                       ZoomOn="Content">
                                                <h:Zoombox.ViewStack>
                                                    <h:ZoomboxView>250 500 500 500</h:ZoomboxView>
                                                </h:Zoombox.ViewStack>
                                                <ContentPresenter Content="{Binding}" />
                                            </h:Zoombox>
                                        </Border>
                                    </DataTemplate>
                                </TabControl.ContentTemplate>
                            </TabControl>
                        </DockPanel>
                    </h:GridSplitterBox>
                </DockPanel>
            </h:GridSplitterBox>
        </Border>
    </Grid>
</h:MainWindow>
